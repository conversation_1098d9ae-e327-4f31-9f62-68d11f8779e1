#!/usr/bin/env python3
"""
S3 Setup Script for Ampersand Capital Portal

This script helps set up your S3 bucket with proper configuration for the portal.
Run this script after creating your S3 bucket and setting up AWS credentials.

Usage:
    python scripts/setup_s3.py --bucket-name your-bucket-name --region ap-south-1
"""

import boto3
import json
import argparse
import sys
from botocore.exceptions import ClientError


def create_bucket_policy(bucket_name):
    """
    Create a bucket policy that allows the application to access the bucket
    while keeping documents private.
    """
    policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Sid": "DenyPublicAccess",
                "Effect": "Deny",
                "Principal": "*",
                "Action": "s3:GetObject",
                "Resource": f"arn:aws:s3:::{bucket_name}/*",
                "Condition": {
                    "StringNotEquals": {
                        "aws:PrincipalServiceName": "s3.amazonaws.com"
                    }
                }
            }
        ]
    }
    return json.dumps(policy, indent=2)


def setup_s3_bucket(bucket_name, region):
    """
    Set up S3 bucket with proper configuration for the portal.
    """
    try:
        # Initialize S3 client
        s3_client = boto3.client('s3', region_name=region)
        
        print(f"Setting up S3 bucket: {bucket_name}")
        
        # Check if bucket exists
        try:
            s3_client.head_bucket(Bucket=bucket_name)
            print(f"✓ Bucket {bucket_name} exists")
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                print(f"✗ Bucket {bucket_name} does not exist. Please create it first.")
                return False
            else:
                print(f"✗ Error checking bucket: {e}")
                return False
        
        # Enable versioning
        try:
            s3_client.put_bucket_versioning(
                Bucket=bucket_name,
                VersioningConfiguration={'Status': 'Enabled'}
            )
            print("✓ Bucket versioning enabled")
        except ClientError as e:
            print(f"✗ Error enabling versioning: {e}")
        
        # Set up server-side encryption
        try:
            s3_client.put_bucket_encryption(
                Bucket=bucket_name,
                ServerSideEncryptionConfiguration={
                    'Rules': [
                        {
                            'ApplyServerSideEncryptionByDefault': {
                                'SSEAlgorithm': 'AES256'
                            }
                        }
                    ]
                }
            )
            print("✓ Server-side encryption configured")
        except ClientError as e:
            print(f"✗ Error setting up encryption: {e}")
        
        # Block public access
        try:
            s3_client.put_public_access_block(
                Bucket=bucket_name,
                PublicAccessBlockConfiguration={
                    'BlockPublicAcls': True,
                    'IgnorePublicAcls': True,
                    'BlockPublicPolicy': True,
                    'RestrictPublicBuckets': True
                }
            )
            print("✓ Public access blocked")
        except ClientError as e:
            print(f"✗ Error blocking public access: {e}")
        
        # Set up lifecycle policy to manage old versions
        try:
            s3_client.put_bucket_lifecycle_configuration(
                Bucket=bucket_name,
                LifecycleConfiguration={
                    'Rules': [
                        {
                            'ID': 'DeleteOldVersions',
                            'Status': 'Enabled',
                            'NoncurrentVersionExpiration': {
                                'NoncurrentDays': 90
                            }
                        }
                    ]
                }
            )
            print("✓ Lifecycle policy configured (old versions deleted after 90 days)")
        except ClientError as e:
            print(f"✗ Error setting up lifecycle policy: {e}")
        
        print(f"\n✓ S3 bucket {bucket_name} is configured for Ampersand Capital Portal")
        print("\nNext steps:")
        print("1. Update your .env.production file with the correct bucket name")
        print("2. Set your AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY")
        print("3. Remove or comment out AWS_S3_ENDPOINT_URL for production")
        print("4. Test file uploads in your application")
        
        return True
        
    except Exception as e:
        print(f"✗ Error setting up S3 bucket: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Set up S3 bucket for Ampersand Capital Portal')
    parser.add_argument('--bucket-name', required=True, help='S3 bucket name')
    parser.add_argument('--region', default='ap-south-1', help='AWS region (default: ap-south-1)')
    
    args = parser.parse_args()
    
    print("Ampersand Capital Portal - S3 Setup")
    print("=" * 40)
    
    success = setup_s3_bucket(args.bucket_name, args.region)
    
    if success:
        print("\n🎉 S3 setup completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ S3 setup failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
