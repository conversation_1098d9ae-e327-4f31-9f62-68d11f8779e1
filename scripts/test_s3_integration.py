#!/usr/bin/env python3
"""
S3 Integration Test Script for Ampersand Capital Portal

This script tests the S3 integration to ensure everything is working correctly.

Usage:
    python scripts/test_s3_integration.py
"""

import os
import sys
import django
from io import BytesIO
from django.core.files.uploadedfile import SimpleUploadedFile

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'amportal_backend.settings')
django.setup()

from django.conf import settings
from django.core.files.storage import default_storage
from accounts.models import Document, Folio, Client, User
import boto3
from botocore.exceptions import ClientError


def test_s3_connection():
    """Test basic S3 connection and bucket access."""
    print("Testing S3 connection...")
    
    try:
        if hasattr(settings, 'AWS_S3_ENDPOINT_URL') and settings.AWS_S3_ENDPOINT_URL:
            # LocalStack
            s3_client = boto3.client(
                's3',
                endpoint_url=settings.AWS_S3_ENDPOINT_URL,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_S3_REGION_NAME
            )
            print("✓ Using LocalStack endpoint")
        else:
            # Real S3
            s3_client = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_S3_REGION_NAME
            )
            print("✓ Using real S3")
        
        # Test bucket access
        response = s3_client.head_bucket(Bucket=settings.AWS_STORAGE_BUCKET_NAME)
        print(f"✓ Successfully connected to bucket: {settings.AWS_STORAGE_BUCKET_NAME}")
        return True
        
    except ClientError as e:
        print(f"✗ S3 connection failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False


def test_file_upload():
    """Test file upload to S3."""
    print("\nTesting file upload...")
    
    try:
        # Create a test file
        test_content = b"This is a test document for S3 integration testing."
        test_file = SimpleUploadedFile(
            "test_document.txt",
            test_content,
            content_type="text/plain"
        )
        
        # Upload using Django's default storage
        file_path = default_storage.save("test/test_document.txt", test_file)
        print(f"✓ File uploaded successfully: {file_path}")
        
        # Verify file exists
        if default_storage.exists(file_path):
            print("✓ File exists in storage")
        else:
            print("✗ File not found in storage")
            return False
        
        # Get file URL
        file_url = default_storage.url(file_path)
        print(f"✓ File URL generated: {file_url[:50]}...")
        
        # Clean up
        default_storage.delete(file_path)
        print("✓ Test file cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ File upload failed: {e}")
        return False


def test_document_model():
    """Test document upload through the Document model."""
    print("\nTesting Document model integration...")
    
    try:
        # Create a test file
        test_content = b"This is a test company document."
        test_file = SimpleUploadedFile(
            "test_company_doc.pdf",
            test_content,
            content_type="application/pdf"
        )
        
        # Create a test document
        document = Document.objects.create(
            document_type='Company',
            subtype='monthly_fund_performance',
            uploaded_file=test_file,
            year=2024,
            month=11
        )
        
        print(f"✓ Document created with ID: {document.id}")
        print(f"✓ File path: {document.uploaded_file.name}")
        
        # Test file URL generation
        file_url = document.uploaded_file.url
        print(f"✓ Document URL generated: {file_url[:50]}...")
        
        # Clean up
        document.delete()
        print("✓ Test document cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ Document model test failed: {e}")
        return False


def test_storage_settings():
    """Test storage configuration."""
    print("\nTesting storage settings...")
    
    print(f"AWS_STORAGE_BUCKET_NAME: {settings.AWS_STORAGE_BUCKET_NAME}")
    print(f"AWS_S3_REGION_NAME: {settings.AWS_S3_REGION_NAME}")
    print(f"USE_LOCALSTACK: {getattr(settings, 'USE_LOCALSTACK', 'Not set')}")
    
    if hasattr(settings, 'AWS_S3_ENDPOINT_URL') and settings.AWS_S3_ENDPOINT_URL:
        print(f"AWS_S3_ENDPOINT_URL: {settings.AWS_S3_ENDPOINT_URL}")
        print("✓ Using LocalStack configuration")
    else:
        print("AWS_S3_ENDPOINT_URL: Not set (using real S3)")
        print("✓ Using real S3 configuration")
    
    # Check storage backend
    storage_backend = settings.STORAGES['default']['BACKEND']
    print(f"Default storage backend: {storage_backend}")
    
    if 'PrivateMediaStorage' in storage_backend:
        print("✓ Using custom PrivateMediaStorage")
    elif 'S3Boto3Storage' in storage_backend:
        print("✓ Using S3Boto3Storage")
    else:
        print("✗ Unexpected storage backend")
        return False
    
    return True


def main():
    """Run all S3 integration tests."""
    print("Ampersand Capital Portal - S3 Integration Test")
    print("=" * 50)
    
    tests = [
        ("Storage Settings", test_storage_settings),
        ("S3 Connection", test_s3_connection),
        ("File Upload", test_file_upload),
        ("Document Model", test_document_model),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("\n🎉 All tests passed! S3 integration is working correctly.")
        return 0
    else:
        print(f"\n❌ {len(tests) - passed} test(s) failed. Please check the configuration.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
