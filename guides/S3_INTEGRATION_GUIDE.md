# S3 Integration Guide for Ampersand Capital Portal

This guide walks you through integrating real AWS S3 storage into your Ampersand Capital portal, replacing the LocalStack development setup.

## Prerequisites

1. **AWS Account** with S3 access
2. **IAM User** with S3 permissions
3. **S3 Bucket** created in your preferred region

## Step 1: Create AWS Resources

### 1.1 Create S3 Bucket

1. Go to AWS S3 Console
2. Click "Create bucket"
3. Choose a unique bucket name (e.g., `ampersand-capital-documents-prod`)
4. Select region: `ap-south-1` (Asia Pacific - Mumbai)
5. Keep "Block all public access" enabled
6. Enable "Bucket Versioning"
7. Enable "Default encryption" with Amazon S3 managed keys (SSE-S3)

### 1.2 Create IAM User

1. Go to AWS IAM Console
2. Create a new user: `ampersand-portal-s3-user`
3. Attach the following policy:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket",
                "s3:GetObjectVersion",
                "s3:PutObjectAcl"
            ],
            "Resource": [
                "arn:aws:s3:::your-bucket-name",
                "arn:aws:s3:::your-bucket-name/*"
            ]
        }
    ]
}
```

4. Generate Access Key ID and Secret Access Key
5. **Save these credentials securely**

## Step 2: Configure Your Application

### 2.1 Update Environment Variables

**For Development (.env):**
```bash
# Keep LocalStack for development
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=aj-amportal
AWS_STORAGE_BUCKET_NAME=my-first-bucket
AWS_S3_REGION_NAME=ap-south-1
AWS_S3_ENDPOINT_URL=http://localhost:4566
```

**For Production (.env.production):**
```bash
# Real S3 credentials
AWS_ACCESS_KEY_ID=your_real_access_key_id
AWS_SECRET_ACCESS_KEY=your_real_secret_access_key
AWS_STORAGE_BUCKET_NAME=your-production-bucket-name
AWS_S3_REGION_NAME=ap-south-1
# AWS_S3_ENDPOINT_URL=  # Leave empty for real S3
```

### 2.2 Run S3 Setup Script

```bash
# Install boto3 if not already installed
pip install boto3

# Run the setup script
python scripts/setup_s3.py --bucket-name your-production-bucket-name --region ap-south-1
```

## Step 3: Test the Integration

### 3.1 Test File Upload

1. Set `DJANGO_ENV=production` in your environment
2. Start your Django application
3. Try uploading a document through the staff portal
4. Check your S3 bucket to verify the file was uploaded

### 3.2 Test File Download

1. Try downloading a document through the client portal
2. Verify that signed URLs are generated correctly
3. Check that URLs expire after 1 hour

## Step 4: Security Considerations

### 4.1 File Access Control

- All documents are stored with `private` ACL
- Access is controlled through signed URLs
- URLs expire after 1 hour for security

### 4.2 Encryption

- Server-side encryption (SSE-S3) is enabled by default
- Files are encrypted at rest in S3

### 4.3 Backup and Versioning

- Bucket versioning is enabled
- Old versions are automatically deleted after 90 days
- Consider setting up cross-region replication for critical data

## Step 5: Monitoring and Maintenance

### 5.1 CloudWatch Monitoring

Set up CloudWatch alarms for:
- S3 bucket size
- Number of requests
- Error rates

### 5.2 Cost Optimization

- Monitor S3 costs in AWS Cost Explorer
- Consider using S3 Intelligent Tiering for cost optimization
- Set up lifecycle policies for old documents

## Troubleshooting

### Common Issues

1. **Access Denied Errors**
   - Check IAM permissions
   - Verify bucket policy
   - Ensure credentials are correct

2. **File Upload Failures**
   - Check bucket name in environment variables
   - Verify region settings
   - Check network connectivity

3. **Signed URL Issues**
   - Verify `AWS_QUERYSTRING_AUTH = True`
   - Check URL expiration settings
   - Ensure proper IAM permissions

### Debug Commands

```bash
# Test AWS credentials
aws s3 ls s3://your-bucket-name

# Check Django settings
python manage.py shell
>>> from django.conf import settings
>>> print(settings.AWS_STORAGE_BUCKET_NAME)
>>> print(settings.USE_LOCALSTACK)
```

## Migration Checklist

- [ ] AWS S3 bucket created
- [ ] IAM user created with proper permissions
- [ ] Environment variables updated
- [ ] S3 setup script executed
- [ ] File upload tested
- [ ] File download tested
- [ ] Signed URLs working
- [ ] Monitoring set up
- [ ] Backup strategy implemented

## Production Deployment

When deploying to production:

1. Set `DJANGO_ENV=production`
2. Ensure `.env.production` has correct S3 credentials
3. Run `python manage.py collectstatic` to upload static files to S3
4. Test all file operations
5. Monitor S3 costs and usage

## Support

For issues with this integration:
1. Check the troubleshooting section above
2. Review Django logs for error details
3. Check AWS CloudTrail for S3 API calls
4. Verify IAM permissions and bucket policies
