"""
Custom S3 storage classes for Ampersand Capital portal.
Provides secure file storage with proper access controls for financial documents.
"""

from storages.backends.s3boto3 import S3Boto3Storage
from django.conf import settings
import os


class PrivateMediaStorage(S3Boto3Storage):
    """
    Custom S3 storage for private media files (documents).
    All files are stored privately and accessed via signed URLs.
    """
    bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    default_acl = 'private'
    file_overwrite = False
    custom_domain = False  # Don't use custom domain for private files
    querystring_auth = True  # Use signed URLs
    querystring_expire = 3600  # URLs expire after 1 hour
    
    def __init__(self, *args, **kwargs):
        kwargs['bucket_name'] = self.bucket_name
        kwargs['default_acl'] = self.default_acl
        kwargs['file_overwrite'] = self.file_overwrite
        kwargs['custom_domain'] = self.custom_domain
        kwargs['querystring_auth'] = self.querystring_auth
        kwargs['querystring_expire'] = self.querystring_expire
        super().__init__(*args, **kwargs)
    
    def get_object_parameters(self, name):
        """
        Set object parameters for uploaded files.
        """
        params = super().get_object_parameters(name)
        
        # Add server-side encryption
        params['ServerSideEncryption'] = 'AES256'
        
        # Set content type based on file extension
        _, ext = os.path.splitext(name)
        if ext.lower() == '.pdf':
            params['ContentType'] = 'application/pdf'
        elif ext.lower() in ['.xlsx', '.xls']:
            params['ContentType'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif ext.lower() == '.csv':
            params['ContentType'] = 'text/csv'
        
        return params


class PublicStaticStorage(S3Boto3Storage):
    """
    Custom S3 storage for public static files.
    Used for CSS, JS, and other static assets that can be publicly accessible.
    """
    bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    location = 'static'
    default_acl = 'public-read'
    file_overwrite = True
    querystring_auth = False
    
    def __init__(self, *args, **kwargs):
        kwargs['bucket_name'] = self.bucket_name
        kwargs['location'] = self.location
        kwargs['default_acl'] = self.default_acl
        kwargs['file_overwrite'] = self.file_overwrite
        kwargs['querystring_auth'] = self.querystring_auth
        super().__init__(*args, **kwargs)
