from django.utils.functional import SimpleLazyObject
from rest_framework_simplejwt.authentication import JWTAuthentication

class JWTAuthenticationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        # Endpoints that should not have JWT tokens processed
        self.unauthenticated_paths = [
            '/api/password-reset/request/',
            '/api/password-reset/confirm/',
            '/api/login/',
        ]

    def __call__(self, request):
        # Skip JWT processing for unauthenticated endpoints
        should_skip_jwt = any(request.path.startswith(path) for path in self.unauthenticated_paths)

        if not should_skip_jwt:
            access_token = request.COOKIES.get('access_token')
            if access_token:
                request.META['HTTP_AUTHORIZATION'] = f'Bearer {access_token}'

        response = self.get_response(request)
        return response