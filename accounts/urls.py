from django.contrib import admin
from django.urls import include, path

from accounts.views import PasswordResetRequestView, SetNewPasswordView, LogoutView, LoginView, CustomTokenRefreshView, SessionValidationView, EmailHealthCheckView
# from .views import RegisterStaff, RegisterClient

urlpatterns = [
    path('logout/', LogoutView.as_view(), name='logout'),
    path('token/refresh/', CustomTokenRefreshView.as_view(), name='token_refresh'),
    path('auth/validate/', SessionValidationView.as_view(), name='session_validate'),
    path('login/', LoginView.as_view(), name='login'),
    path('password-reset/request/', PasswordResetRequestView.as_view(), name='password-reset-request'),
    path('password-reset/confirm/<str:uidb64>/<str:token>/', SetNewPasswordView.as_view(), name='password-reset-confirm'),
    path('email/health/', EmailHealthCheckView.as_view(), name='email-health-check'),
]