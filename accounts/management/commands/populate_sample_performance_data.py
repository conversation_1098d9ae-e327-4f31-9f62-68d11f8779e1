"""
Management command to populate sample performance data for testing charts.
"""

from django.core.management.base import BaseCommand
from accounts.models import FundNAVHistory, BenchmarkHistory
from datetime import date, timedelta
from dateutil.relativedelta import relativedelta
from decimal import Decimal
import random


class Command(BaseCommand):
    help = 'Populate sample performance data for testing charts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--months',
            type=int,
            default=60,
            help='Number of months of historical data to generate (default: 60)',
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before populating',
        )

    def handle(self, *args, **options):
        months = options['months']
        clear_data = options['clear']

        if clear_data:
            self.stdout.write('Clearing existing performance data...')
            FundNAVHistory.objects.all().delete()
            BenchmarkHistory.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing data cleared.'))

        self.stdout.write(f'Generating {months} months of sample performance data...')

        # Starting values
        fund_nav = Decimal('100.0000')
        benchmark_value = Decimal('100.0000')
        
        # Generate data for each month
        today = date.today()
        
        for i in range(months, 0, -1):  # Go backwards from today
            # Calculate the date (last day of each month)
            target_date = today - relativedelta(months=i)
            # Get the last day of that month
            next_month = target_date.replace(day=28) + timedelta(days=4)
            last_day = next_month - timedelta(days=next_month.day)
            
            # Generate realistic performance variations
            # Fund performance: slightly better than benchmark with more volatility
            fund_monthly_return = random.uniform(-0.08, 0.12)  # -8% to +12% monthly
            benchmark_monthly_return = random.uniform(-0.06, 0.08)  # -6% to +8% monthly
            
            # Apply returns
            fund_nav = fund_nav * (Decimal('1') + Decimal(str(fund_monthly_return)))
            benchmark_value = benchmark_value * (Decimal('1') + Decimal(str(benchmark_monthly_return)))
            
            # Create or update records
            FundNAVHistory.objects.update_or_create(
                date=last_day,
                defaults={'nav': fund_nav}
            )
            
            BenchmarkHistory.objects.update_or_create(
                date=last_day,
                defaults={'value': benchmark_value}
            )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully populated {months} months of sample performance data!'
            )
        )
        
        # Show summary
        fund_count = FundNAVHistory.objects.count()
        benchmark_count = BenchmarkHistory.objects.count()
        
        self.stdout.write(f'Total Fund NAV records: {fund_count}')
        self.stdout.write(f'Total Benchmark records: {benchmark_count}')
        
        # Show latest values
        latest_fund = FundNAVHistory.objects.order_by('-date').first()
        latest_benchmark = BenchmarkHistory.objects.order_by('-date').first()
        
        if latest_fund:
            self.stdout.write(f'Latest Fund NAV ({latest_fund.date}): {latest_fund.nav}')
        if latest_benchmark:
            self.stdout.write(f'Latest Benchmark ({latest_benchmark.date}): {latest_benchmark.value}')
