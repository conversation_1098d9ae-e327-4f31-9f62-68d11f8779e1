
# Django
DJANGO_SECRET="fu@^7%=mrj!bb!-_k6z6kkh=t!zt(xdbue398$fi!_=j@+vwlu+ampersand"
DEBUG=False
DJANGO_ENV=production

# Database
DB_ENGINE=django.db.backends.postgresql
DB_NAME=amportal_db
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# AWS - Real S3 (Production)
AWS_ACCESS_KEY_ID=your_real_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_real_aws_secret_access_key
AWS_STORAGE_BUCKET_NAME=your-production-bucket-name
AWS_S3_REGION_NAME=ap-south-1
# AWS_S3_ENDPOINT_URL=  # Leave empty for real S3

# Email
DEFAULT_FROM_EMAIL=<EMAIL>
EMAIL_HOST_USER=your_email_user
EMAIL_HOST_PASSWORD=your_email_password


EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=djibpeizldfyxqre
DJANGO_SECRET="fu@^7%=mrj!bb!-_k6z6kkh=t!zt(xdbue398$fi!_=j@+vwlu+ampersand"