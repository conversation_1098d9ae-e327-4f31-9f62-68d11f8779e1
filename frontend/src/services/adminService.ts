import api from './api';

interface DashboardStats {
  staffCount: number;
  clientCount: number;
  folioCount: number;
  adminName: string;
}

export const getDashboardStats = async (): Promise<DashboardStats> => {
  const response = await api.get('/admin/dashboard-stats/');
  return response.data;
};


interface StaffMember {
    id: number;
    user: number;
    email: string;
    first_name: string;
    last_name: string;
}

export const getStaffList = async (): Promise<StaffMember[]> => {
    const response = await api.get('/admin/staff-list/');
    return response.data.staff;
}

interface ClientMember {
    id: number;
    user: number;
    email: string;
    first_name: string;
    last_name: string;
    pan: string;
}

export const getClientList = async (): Promise<ClientMember[]> => {
    const response = await api.get('/admin/client-list/');
    return response.data.clients;
}

interface RegisterStaffData {
    email: string;
    first_name: string;
    last_name: string;
}

export const registerStaff = async (data: RegisterStaffData): Promise<StaffMember> => {
    const response = await api.post('/admin/register-staff/', data);
    return response.data;
}

export const searchStaff = async (query: string): Promise<StaffMember[]> => {
    const response = await api.get('/admin/search-staff/', { params: { query } });
    return response.data;
};

export const searchClients = async (query: string): Promise<ClientMember[]> => {
    const response = await api.get('/admin/search-clients/', { params: { name_query: query } });
    return response.data;
};