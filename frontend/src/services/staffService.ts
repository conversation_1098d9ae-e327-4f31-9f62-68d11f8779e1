import api from './api';

interface ClientSearchData {
    name_query?: string;
    folio_number_query?: string;
}

interface ClientSearchResult {
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    folio_numbers: Record<string, boolean>;
}

export const searchClients = async (data: ClientSearchData): Promise<ClientSearchResult[]> => {
    const response = await api.get('/staff/search-clients/', { params: data });
    return response.data;
}


interface RegisterClientData {
    email: string;
    first_name: string;
    last_name: string;
    pan: string;
    folio_number: string;
}

interface Client {
    id: number;
    user: number;
    pan: string;
    email: string;
    first_name: string;
    last_name: string;
}

export const registerClient = async (data: RegisterClientData): Promise<Client> => {
    const response = await api.post('/staff/register-client/', data);
    return response.data;
}

export const uploadDocument = async (formData: FormData): Promise<any> => {
    const response = await api.post('/staff/upload/', formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
    return response.data;
};

interface DocumentFilters {
    year?: number;
    month?: number;
}

interface Document {
    id: number;
    document_type: string;
    subtype: string;
    year: number;
    month: number;
    file_name: string;
    file_size: number;
    created_at: string;
    last_updated_at: string;
    folio_number: number;
    client_name: string;
}

interface DocumentManagerData {
    company_documents: Document[];
    client_documents: { folio_number: number; client_name: string; documents: Document[] }[];
    filters_applied: DocumentFilters;
    total_documents: { total: number; company: number; client: number; };
}

export const getDocuments = async (filters: DocumentFilters): Promise<DocumentManagerData> => {
    const response = await api.get('/staff/documents/', { params: filters });
    return response.data;
}

export const downloadDocument = async (documentId: number): Promise<Blob> => {
    const response = await api.get(`/staff/download-document/${documentId}/`, {
        responseType: 'blob',
    });
    return response.data;
};

export const createFolio = async (client_username: string, folio_number: string): Promise<any> => {
    const response = await api.post('/staff/create-folio/', { client_username, folio_number });
    return response.data;
};

export const getDashboardStats = async (): Promise<any> => {
    const response = await api.get('/staff/dashboard-stats/');
    return response.data;
};

export const terminateFolio = async (folioNumber: string): Promise<any> => {
    const response = await api.delete('/staff/create-folio/', { data: { folio_number: folioNumber } });
    return response.data;
};