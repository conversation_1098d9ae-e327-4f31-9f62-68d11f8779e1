
import axios from 'axios';

// Main API instance with credentials for authenticated requests
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  withCredentials: true,
});

// Separate API instance without credentials for unauthenticated requests
const unauthenticatedApi = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  withCredentials: false, // Don't send cookies
});

// Common error handler for both instances
const errorHandler = (error: any) => {
  if (error.response && error.response.data) {
    // Backend-specific error structure
    if (error.response.data.error) {
      return Promise.reject(new Error(error.response.data.error));
    }
    if (error.response.data.detail) {
      return Promise.reject(new Error(error.response.data.detail));
    }
    // Handle validation errors (e.g., from Django Rest Framework)
    if (typeof error.response.data === 'object') {
      const messages = Object.values(error.response.data).flat();
      if (messages.length > 0) {
        return Promise.reject(new Error(messages.join(' ')));
      }
    }
  }
  return Promise.reject(new Error(error.message || 'An unexpected error occurred.'));
};

// Apply error handler to both instances
api.interceptors.response.use((response) => response, errorHandler);
unauthenticatedApi.interceptors.response.use((response) => response, errorHandler);

export default api;
export { unauthenticatedApi };
