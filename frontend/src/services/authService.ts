
import api, { unauthenticatedApi } from './api';


// Define the LoginData interface based on your backend serializer
interface LoginData {
  username: string;
  password: string;
}

// Define the User interface based on your backend response
interface User {
  id: number;
  username: string;
  role: string;
}

export const login = async (credentials: LoginData): Promise<User> => {
  const response = await api.post('/login/', credentials);
  return response.data;
};

export const logout = async () => {
  await api.post('/logout/');
};

export const validateSession = async (): Promise<User> => {
    const response = await api.get('/auth/validate/');
    return response.data;
}

export const requestPasswordReset = async (email: string): Promise<{ detail: string }> => {
  const response = await unauthenticatedApi.post('/password-reset/request/', { email });
  return response.data;
};

export const resetPasswordConfirm = async (uidb64: string, token: string, password: string): Promise<{ detail: string }> => {
  const response = await unauthenticatedApi.patch(`/password-reset/confirm/${uidb64}/${token}/`, { password });
  return response.data;
};
