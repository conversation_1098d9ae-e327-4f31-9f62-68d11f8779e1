import React, { useState, useEffect } from 'react';
import { uploadDocument, getDocuments } from '../../services/staffService';
import { getErrorMessage } from '../../utils/errorUtils';
import {
  TextField, Button, Box, Typography, Select, MenuItem, FormControl, InputLabel, CircularProgress, Alert, Grid
} from '@mui/material';

const UploadDocument: React.FC = () => {
  const [formData, setFormData] = useState<any>({});
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [existingDocuments, setExistingDocuments] = useState<any[]>([]);

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        const docs = await getDocuments({});
        const allDocs = [...docs.company_documents];
        docs.client_documents.forEach(folio => {
          allDocs.push(...folio.documents);
        });
        setExistingDocuments(allDocs);
      } catch (error) {
        console.error('Failed to fetch documents', error);
      }
    };
    fetchDocuments();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name!]: value });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Clear any previous errors
      setError(null);

      // Check file type
      if (!selectedFile.name.toLowerCase().endsWith('.pdf')) {
        setError('Only PDF files are allowed.');
        e.target.value = ''; // Clear the input
        return;
      }

      // Check file size (2MB = 2 * 1024 * 1024 bytes)
      const maxSize = 2 * 1024 * 1024; // 2MB in bytes
      if (selectedFile.size > maxSize) {
        const fileSizeMB = (selectedFile.size / (1024 * 1024)).toFixed(2);
        setError(`File size must be less than 2MB. Current file size is ${fileSizeMB}MB.`);
        e.target.value = ''; // Clear the input
        return;
      }

      setFile(selectedFile);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    const { document_type, year, month, folio_number } = formData;

    const existingDoc = existingDocuments.find(doc => 
      doc.document_type === document_type &&
      doc.year === parseInt(year, 10) &&
      doc.month === parseInt(month, 10) &&
      (document_type === 'Client' ? doc.folio_number === parseInt(folio_number, 10) : true)
    );

    if (existingDoc) {
      const confirmed = window.confirm('A document of this type for the selected month and year already exists. Do you want to replace it?');
      if (!confirmed) {
        setLoading(false);
        return;
      }
    }

    // Validate that a file is selected
    if (!file) {
      setError('Please select a PDF file to upload.');
      setLoading(false);
      return;
    }

    const data = new FormData();
    Object.keys(formData).forEach(key => {
      data.append(key, formData[key]);
    });

    if (file) {
      const fileExtension = file.name.split('.').pop();
      const newFileName = `${document_type}_${year}_${month}.${fileExtension}`;
      data.append('uploaded_file', file, newFileName);
    }

    try {
      await uploadDocument(data);
      setSuccess('Document uploaded successfully!');
      setFormData({});
      setFile(null);
    } catch (err: any) {
      setError(getErrorMessage(err));
    }
    setLoading(false);
  };

  return (
    <Box sx={{ maxWidth: 800, margin: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Upload Document
      </Typography>
      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 3 }}>
        <Grid container spacing={2}>
          <Grid size={{ xs: 12 }}>
            <FormControl fullWidth>
              <InputLabel>Document Type</InputLabel>
              <Select name="document_type" value={formData.document_type || ''} onChange={handleChange as any}>
                <MenuItem value="Company">Company</MenuItem>
                <MenuItem value="Client">Client</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {formData.document_type === 'Company' && (
            <Grid size={{ xs: 12 }}>
              <FormControl fullWidth>
                <InputLabel>Company Subtype</InputLabel>
                <Select name="subtype" value={formData.subtype || ''} onChange={handleChange as any}>
                  <MenuItem value="monthly_fund_performance">Monthly Fund Performance</MenuItem>
                  <MenuItem value="monthly_investor_presentation">Monthly Investor Presentation</MenuItem>
                  <MenuItem value="sebi_quarterly_report">SEBI Quarterly Report</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          )}

          {formData.document_type === 'Client' && (
            <Grid size={{ xs: 12 }}>
              <TextField name="folio_number" label="Folio Number" fullWidth onChange={handleChange} />
            </Grid>
          )}

          <Grid size={{ xs: 6 }}>
            <TextField name="year" label="Year" type="number" fullWidth onChange={handleChange} />
          </Grid>
          <Grid size={{ xs: 6 }}>
            <TextField name="month" label="Month" type="number" fullWidth onChange={handleChange} />
          </Grid>

          {formData.document_type === 'Client' && (
            <>
              <Grid size={{ xs: 6 }}>
                <TextField name="pre_tax_nav" label="Pre-tax NAV" type="number" fullWidth onChange={handleChange} />
              </Grid>
              <Grid size={{ xs: 6 }}>
                <TextField name="post_tax_nav" label="Post-tax NAV" type="number" fullWidth onChange={handleChange} />
              </Grid>
            </>
          )}

          {formData.document_type === 'Company' && formData.subtype === 'monthly_fund_performance' && (
            <>
              <Grid size={{ xs: 6 }}>
                <TextField name="fund_nav" label="Fund NAV" type="number" fullWidth onChange={handleChange} />
              </Grid>
              <Grid size={{ xs: 6 }}>
                <TextField name="benchmark_value" label="Benchmark Value" type="number" fullWidth onChange={handleChange} />
              </Grid>
            </>
          )}

          <Grid size={{ xs: 12 }}>
            <Button variant="contained" component="label">
              Choose File
              <input type="file" accept=".pdf" hidden onChange={handleFileChange} />
            </Button>
            {file && <Typography sx={{ ml: 2, display: 'inline' }}>{file.name}</Typography>}
          </Grid>
        </Grid>

        {error && <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mt: 2 }}>{success}</Alert>}

        <Button type="submit" fullWidth variant="contained" sx={{ mt: 3 }} disabled={loading}>
          {loading ? <CircularProgress size={24} /> : 'Upload'}
        </Button>
      </Box>
    </Box>
  );
};

export default UploadDocument;
